import React, { useMemo, useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Text, Dimensions, Platform } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import metrics from 'themes/metrics';
import commonStyles from 'themes/commonStyles';
import CommonButton from 'components/CommonButton';
import { Avatar } from '@ui-kitten/components';
import FormInput from 'components/FormInput';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { REGEX_INPUT } from 'constants/authentication';
import { useForm, Controller } from 'react-hook-form';
import Ionicons from 'react-native-vector-icons/Ionicons';
import useProfileUpdate from './hook';
import colors from 'themes/colors';
import cities from 'assets/city.json';
import districts from 'assets/district.json';
import wards from 'assets/ward.json';
import { UpdateUserRequest } from 'types/user';
import ChangeEmailModal from './components/ChangeEmailModal';

interface FormData {
  firstname: string;
  lastname: string;
  gender: 'MALE' | 'FEMALE';
  phone: string | null;
  email: string;
  birthday: string;
  ward: string;
  wardId: number;
  district: string;
  districtId: number;
  city: string;
  provinceId: number;
  address: string;
}

const schema = yup.object({
  firstname: yup.string().required('Tên là bắt buộc').trim(),
  lastname: yup.string().required('Họ là bắt buộc').trim(),
  gender: yup.string().required('Giới tính là bắt buộc'),
  phone: yup
    .string()
    .nullable()
    .matches(REGEX_INPUT.PHONE, { message: 'Định dạng số điện thoại không hợp lệ', excludeEmptyString: true }),
  email: yup
    .string()
    .required('Email là bắt buộc')
    .trim()
    .matches(REGEX_INPUT.EMAIL, 'Định dạng email không hợp lệ'),
  birthday: yup
    .string()
    .required('Ngày sinh là bắt buộc')
    .matches(/^\d{4}-\d{2}-\d{2}$/, 'Ngày sinh phải có định dạng YYYY-MM-DD')
    .test('is-past-date', 'Ngày sinh không thể ở tương lai', (value) => {
      const today = new Date();
      const birthday = new Date(value);
      return birthday <= today;
    }),
  ward: yup.string().required('Phường/Xã là bắt buộc').trim(),
  wardId: yup.number().required('Phường/Xã là bắt buộc').min(1, 'Vui lòng chọn phường/xã hợp lệ'),
  district: yup.string().required('Quận/Huyện là bắt buộc').trim(),
  districtId: yup.number().required('Quận/Huyện là bắt buộc').min(1, 'Vui lòng chọn quận/huyện hợp lệ'),
  city: yup.string().required('Tỉnh/Thành phố là bắt buộc').trim(),
  provinceId: yup.number().required('Tỉnh/Thành phố là bắt buộc').min(1, 'Vui lòng chọn tỉnh/thành phố hợp lệ'),
  address: yup.string().required('Địa chỉ là bắt buộc').trim(),
});

const ProfilePanel = () => {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [showPhoneModal, setShowPhoneModal] = useState(false);

  const { avatarUri, uploading, handleChoosePhoto, error: avatarError, uploadAvatar, getUser, updateUser } = useProfileUpdate(
    user?.avatar || null,
    (uri) => setUser((prev: any) => ({ ...prev, avatar: uri }))
  );

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        const userData = await getUser();
        setUser(userData);
      } catch (error) {
        setSubmitError('Không thể tải thông tin người dùng. Vui lòng thử lại.');
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [getUser]);

  useEffect(() => {
    if (avatarError || submitError) {
      const timer = setTimeout(() => {
        setSubmitError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [avatarError, submitError]);

  const parsedBirthday = useMemo(() => {
    if (!user?.birthday) return undefined;
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(user.birthday)) return undefined;
    const date = new Date(user.birthday);
    return isNaN(date.getTime()) ? undefined : date;
  }, [user?.birthday]);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      firstname: '',
      lastname: '',
      gender: 'MALE',
      phone: null,
      email: '',
      birthday: '',
      ward: '',
      wardId: 0,
      district: '',
      districtId: 0,
      city: '',
      provinceId: 0,
      address: '',
    },
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    if (user) {
      reset({
        firstname: user.firstname || user.firstName || '',
        lastname: user.lastname || user.lastName || '',
        gender: user.gender === 'MALE' ? 'MALE' : 'FEMALE',
        phone: user.phone || null,
        email: user.email || '',
        birthday: user.birthday && /^\d{4}-\d{2}-\d{2}$/.test(user.birthday) ? user.birthday : '',
        ward: user.ward || '',
        wardId: user.wardId || 0,
        district: user.district || '',
        districtId: user.districtId || 0,
        city: user.city || '',
        provinceId: user.provinceId || 0,
        address: user.address || '',
      });
    }
  }, [user, reset]);

  const selectedProvince = watch('provinceId');
  const selectedDistrict = watch('districtId');

  const filteredDistricts = useMemo(
    () => (selectedProvince ? districts.filter((district) => Number(district.city_id) === Number(selectedProvince)) : []),
    [selectedProvince]
  );

  const filteredWards = useMemo(
    () => (selectedDistrict ? wards.filter((ward) => Number(ward.districtId) === Number(selectedDistrict)) : []),
    [selectedDistrict]
  );

  const handleCityChange = useCallback(
    (cityId: number) => {
      const selectedCity = cities.find((city) => Number(city.id) === cityId);
      if (selectedCity) {
        setValue('provinceId', cityId, { shouldValidate: true });
        setValue('city', selectedCity.name, { shouldValidate: true });
        setValue('districtId', 0, { shouldValidate: true });
        setValue('district', '', { shouldValidate: true });
        setValue('wardId', 0, { shouldValidate: true });
        setValue('ward', '', { shouldValidate: true });
      }
    },
    [setValue]
  );

  const handleDistrictChange = useCallback(
    (districtId: number) => {
      const selectedDistrict = districts.find((district) => Number(district.id) === districtId);
      if (selectedDistrict) {
        setValue('districtId', districtId, { shouldValidate: true });
        setValue('district', selectedDistrict.name, { shouldValidate: true });
        setValue('wardId', 0, { shouldValidate: true });
        setValue('ward', '', { shouldValidate: true });
      }
    },
    [setValue]
  );

  const handleWardChange = useCallback(
    (wardId: number) => {
      const selectedWard = wards.find((ward) => Number(ward.id) === wardId);
      if (selectedWard) {
        setValue('wardId', wardId, { shouldValidate: true });
        setValue('ward', selectedWard.name, { shouldValidate: true });
      }
    },
    [setValue]
  );

  const onSubmit = useCallback(
    async (values: FormData) => {
      setSubmitError(null);
      setLoading(true);

      let finalAvatarUri = avatarUri;

      if (avatarUri && avatarUri !== user?.avatar && !avatarUri.startsWith('http')) {
        try {
          finalAvatarUri = await uploadAvatar();
          if (!finalAvatarUri) {
            setSubmitError('Không thể tải lên ảnh đại diện. Bạn vẫn có thể lưu các thông tin khác.');
          }
        } catch (err) {
          setSubmitError('Không thể tải lên ảnh đại diện. Bạn vẫn có thể lưu các thông tin khác.');
        }
      }

      const finalValues: UpdateUserRequest = {
        firstname: values.firstname,
        lastname: values.lastname,
        gender: values.gender === 'MALE' ? 'MALE' : 'FEMALE',
        birthday: values.birthday,
        ward: values.ward,
        wardId: values.wardId,
        district: values.district,
        districtId: values.districtId,
        city: values.city,
        provinceId: values.provinceId,
        address: values.address,
        avatar: finalAvatarUri || null,
      };

      try {
        const updatedUser = await updateUser(finalValues);
        setUser(updatedUser);
        setSubmitError(null);
      } catch (err: any) {
        const errorMessage =
          err?.response?.data?.message ||
          err?.response?.data?.errors?.join(', ') ||
          err?.message ||
          'Không thể cập nhật hồ sơ. Vui lòng thử lại.';
        setSubmitError(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    [avatarUri, user?.avatar, uploadAvatar, updateUser]
  );

  const clearErrors = useCallback(() => {
    setSubmitError(null);
  }, []);

  const handleEmailChange = useCallback((newEmail: string) => {
    setUser((prev: any) => ({ ...prev, email: newEmail }));
    setValue('email', newEmail, { shouldValidate: true });
  }, [setValue]);

  const handlePhoneChange = useCallback((newPhone: string) => {
    setUser((prev: any) => ({ ...prev, phone: newPhone }));
    setValue('phone', newPhone, { shouldValidate: true });
  }, [setValue]);

  if (loading && !user) {
    return (
      <View style={[styles.container, styles.center]}>
        <Text style={styles.loadingText}>Đang tải...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.outerWrapper} contentContainerStyle={styles.scrollContent}>
        {/* Avatar Section */}
        <View style={styles.avatarSection}>
          <View style={styles.avatarContainer}>
            <Avatar
              style={styles.avatar}
              source={avatarUri ? { uri: avatarUri } : require('assets/images/boy.png')}
            />
            <TouchableOpacity
              style={[styles.editIcon, uploading && styles.editIconDisabled]}
              onPress={handleChoosePhoto}
              disabled={uploading}
              activeOpacity={0.7}
              accessibilityLabel="Chọn ảnh đại diện"
            >
              <Ionicons
                name={uploading ? 'cloud-upload-outline' : 'camera-outline'}
                size={24}
                color={colors['white']}
              />
            </TouchableOpacity>
          </View>
          {uploading && (
            <View style={styles.uploadingContainer}>
              <Text style={styles.uploadingText}>Đang tải lên...</Text>
            </View>
          )}
        </View>

        {/* Error Message */}
        {(avatarError || submitError) && (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle-outline" size={20} color={colors['Danger/500']} />
            <Text style={styles.errorText}>{avatarError || submitError}</Text>
            <TouchableOpacity onPress={clearErrors} accessibilityLabel="Xóa thông báo lỗi">
              <Ionicons name="close-circle-outline" size={20} color={colors['Gray/400']} />
            </TouchableOpacity>
          </View>
        )}

        {/* Personal Information Card */}
        <View style={styles.cardContainer}>
          <View style={styles.cardHeader}>
            <View style={styles.cardHeaderIcon}>
              <Ionicons name="person-outline" size={24} color={colors['Primary/500']} />
            </View>
            <Text style={styles.cardTitle}>Thông tin cá nhân</Text>
          </View>

          <View style={styles.cardContent}>
            <View style={[commonStyles.rowSpaceBetween, commonStyles.gap4]}>
              <View style={styles.rowForm}>
                <FormInput
                  label="Tên"
                  control={control}
                  required
                  placeholder="Nhập tên"
                  name="firstname"
                  errors={errors?.firstname}
                  disabled={loading}
                />
              </View>
              <View style={styles.rowForm}>
                <FormInput
                  label="Họ"
                  control={control}
                  required
                  placeholder="Nhập họ"
                  name="lastname"
                  errors={errors?.lastname}
                  disabled={loading}
                />
              </View>
            </View>

            <View style={styles.rowForm}>
              <View style={styles.fieldWithButton}>
                <View style={styles.fieldContainer}>
                  <Text style={styles.label}>
                    Email <Text style={styles.required}>*</Text>
                  </Text>
                  <Controller
                    control={control}
                    name="email"
                    render={({ field: { value } }) => (
                      <View style={[styles.input, styles.disabledInput]}>
                        <Text style={styles.disabledText}>{value || 'Chưa có email'}</Text>
                      </View>
                    )}
                  />
                  {errors?.email && <Text style={styles.formErrorText}>{errors.email.message}</Text>}
                </View>
                <TouchableOpacity
                  style={styles.changeButton}
                  onPress={() => setShowEmailModal(true)}
                  activeOpacity={0.7}
                >
                  <Ionicons name="create-outline" size={16} color={colors['Primary/600']} />
                  <Text style={styles.changeButtonText}>Thay đổi</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.rowForm}>
              <View style={styles.fieldWithButton}>
                <View style={styles.fieldContainer}>
                  <Text style={styles.label}>Số điện thoại</Text>
                  <Controller
                    control={control}
                    name="phone"
                    render={({ field: { value } }) => (
                      <View style={[styles.input, styles.disabledInput]}>
                        <Text style={styles.disabledText}>{value || 'Chưa có số điện thoại'}</Text>
                      </View>
                    )}
                  />
                  {errors?.phone && <Text style={styles.formErrorText}>{errors.phone.message}</Text>}
                </View>
                <TouchableOpacity
                  style={styles.changeButton}
                  onPress={() => setShowPhoneModal(true)}
                  activeOpacity={0.7}
                >
                  <Ionicons name="create-outline" size={16} color={colors['Primary/600']} />
                  <Text style={styles.changeButtonText}>Thay đổi</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={[commonStyles.rowSpaceBetween, commonStyles.gap4]}>
              <View style={styles.rowForm}>
                <FormInput
                  label="Ngày sinh"
                  control={control}
                  required
                  placeholder="DD/MM/YYYY"
                  name="birthday"
                  errors={errors?.birthday}
                  inputType="datepicker"
                  dateValue={parsedBirthday}
                  max={new Date()}
                  disabled={loading}
                />
              </View>

              <View style={styles.rowForm}>
                <FormInput
                  label="Giới tính"
                  control={control}
                  required
                  placeholder="Chọn giới tính"
                  name="gender"
                  errors={errors?.gender}
                  inputType="select"
                  options={[
                    { label: 'Nam', value: 'MALE' },
                    { label: 'Nữ', value: 'FEMALE' },
                  ]}
                  disabled={loading}
                />
              </View>
            </View>
          </View>
        </View>

        {/* Address Information Card */}
        <View style={styles.cardContainer}>
          <View style={styles.cardHeader}>
            <View style={styles.cardHeaderIcon}>
              <Ionicons name="location-outline" size={24} color={colors['Primary/500']} />
            </View>
            <Text style={styles.cardTitle}>Thông tin địa chỉ</Text>
          </View>

          <View style={styles.cardContent}>
            <View style={styles.rowForm}>
              <Text style={[styles.label, errors.provinceId && styles.labelError]}>
                Tỉnh/Thành phố <Text style={styles.required}>*</Text>
              </Text>
              <Controller
                control={control}
                name="provinceId"
                render={({ field: { value } }) => (
                  <View style={[styles.pickerContainer, errors.provinceId && styles.pickerContainerError]}>
                    <Picker
                      selectedValue={Number(value) || 0}
                      onValueChange={handleCityChange}
                      style={styles.picker}
                      accessibilityLabel="Chọn tỉnh/thành phố"
                    >
                      <Picker.Item label="Chọn tỉnh/thành phố" value={0} color={colors['Gray/500']} />
                      {cities.map((city) => (
                        <Picker.Item key={city.id} label={city.name} value={Number(city.id)} />
                      ))}
                    </Picker>
                  </View>
                )}
              />
              {(errors.city || errors.provinceId) && (
                <Text style={styles.formErrorText}>{errors.city?.message || errors.provinceId?.message}</Text>
              )}
            </View>

            <View style={styles.rowForm}>
              <Text style={[styles.label, errors.districtId && styles.labelError]}>
                Quận/Huyện <Text style={styles.required}>*</Text>
              </Text>
              <Controller
                control={control}
                name="districtId"
                render={({ field: { value } }) => (
                  <View style={[styles.pickerContainer, errors.districtId && styles.pickerContainerError]}>
                    <Picker
                      selectedValue={Number(value) || 0}
                      onValueChange={handleDistrictChange}
                      style={styles.picker}
                      enabled={!!selectedProvince}
                      accessibilityLabel="Chọn quận/huyện"
                    >
                      <Picker.Item label="Chọn quận/huyện" value={0} color={colors['Gray/500']} />
                      {filteredDistricts.map((district) => (
                        <Picker.Item key={district.id} label={district.name} value={Number(district.id)} />
                      ))}
                    </Picker>
                  </View>
                )}
              />
              {(errors.district || errors.districtId) && (
                <Text style={styles.formErrorText}>{errors.district?.message || errors.districtId?.message}</Text>
              )}
            </View>

            <View style={styles.rowForm}>
              <Text style={[styles.label, errors.wardId && styles.labelError]}>
                Phường/Xã <Text style={styles.required}>*</Text>
              </Text>
              <Controller
                control={control}
                name="wardId"
                render={({ field: { value } }) => (
                  <View style={[styles.pickerContainer, errors.wardId && styles.pickerContainerError]}>
                    <Picker
                      selectedValue={Number(value) || 0}
                      onValueChange={handleWardChange}
                      style={styles.picker}
                      enabled={!!selectedDistrict}
                      accessibilityLabel="Chọn phường/xã"
                    >
                      <Picker.Item label="Chọn phường/xã" value={0} color={colors['Gray/500']} />
                      {filteredWards.map((ward) => (
                        <Picker.Item key={ward.id} label={ward.name} value={Number(ward.id)} />
                      ))}
                    </Picker>
                  </View>
                )}
              />
              {(errors.ward || errors.wardId) && (
                <Text style={styles.formErrorText}>{errors.ward?.message || errors.wardId?.message}</Text>
              )}
            </View>

            <View style={styles.rowForm}>
              <FormInput
                label="Địa chỉ chi tiết"
                control={control}
                required
                placeholder="Nhập địa chỉ chi tiết"
                name="address"
                errors={errors?.address}
                disabled={loading}
              />
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Submit Button */}
      <View style={styles.buttonContainer}>
        <CommonButton
         customStyles={{
                    backgroundColor: colors['Moss/500'] || '#4CAF50',
                    borderColor: colors['Moss/500'] || '#4CAF50',
                  }}
          loading={loading || uploading}
          onPress={handleSubmit(onSubmit)}
        >
          {loading || uploading ? 'Đang lưu...' : 'Lưu thông tin'}
        </CommonButton>
      </View>

      {/* Change Email Modal */}
      <ChangeEmailModal
        visible={showEmailModal}
        onClose={() => setShowEmailModal(false)}
        onSuccess={handleEmailChange}
        currentEmail={user?.email || ''}
      />

      {/* Change Phone Modal */}
      <ChangePhoneModal
        visible={showPhoneModal}
        onClose={() => setShowPhoneModal(false)}
        onSuccess={handlePhoneChange}
        currentPhone={user?.phone || ''}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors['Gray/50'],
  },
  outerWrapper: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: metrics.spacing6,
    paddingBottom: 120,
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Avatar Section
  avatarSection: {
    alignItems: 'center',
    marginBottom: metrics.spacing6,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: metrics.spacing2,
  },
  avatar: {
    width: 140,
    height: 140,
    borderRadius: 70,
    borderColor: colors['white'],
    borderWidth: 4,
    backgroundColor: colors['white'],
    shadowColor: colors['black'],
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  editIcon: {
    backgroundColor: colors['Primary/500'],
    borderRadius: 24,
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 4,
    right: 4,
    borderWidth: 4,
    borderColor: colors['white'],
    shadowColor: colors['Primary/500'],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  editIconDisabled: {
    backgroundColor: colors['Gray/400'],
  },
  uploadingContainer: {
    backgroundColor: colors['Primary/50'],
    paddingHorizontal: metrics.spacing3,
    paddingVertical: metrics.spacing2,
    borderRadius: metrics.radius5,
    borderWidth: 1,
    borderColor: colors['Primary/200'],
  },
  uploadingText: {
    fontSize: 14,
    color: colors['Primary/600'],
    fontWeight: '600',
  },

  // Error Container
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: metrics.spacing4,
    marginBottom: metrics.spacing4,
    backgroundColor: colors['Danger/50'],
    padding: metrics.spacing4,
    borderRadius: metrics.radius4,
    borderWidth: 1,
    borderColor: colors['Danger/100'],
  },
  errorText: {
    color: colors['Danger/700'],
    fontSize: 14,
    marginHorizontal: metrics.spacing2,
    flex: 1,
    fontWeight: '500',
  },

  // Card Container
  cardContainer: {
    marginHorizontal: metrics.spacing4,
    marginBottom: metrics.spacing4,
    backgroundColor: colors['white'],
    borderRadius: metrics.radius5,
    shadowColor: colors['black'],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    overflow: 'hidden',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: metrics.spacing1,
    backgroundColor: colors['Gray/50'],
    borderBottomWidth: 1,
    borderBottomColor: colors['Gray/100'],
  },
  cardHeaderIcon: {
    backgroundColor: colors['Primary/50'],
    padding: metrics.spacing2,
    borderRadius: metrics.radius3,
    marginRight: metrics.spacing3,
  },
  cardTitle: {
    fontSize: 14,
    fontWeight: '700',
    color: colors['Gray/800'],
  },
  cardContent: {
    padding: metrics.spacing4,
  },
  rowForm: {
    marginBottom: metrics.spacing4,
    flex: 1,
  },

  // Form Elements - Cải tiến
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: colors['Gray/700'],
    marginBottom: metrics.spacing2,
  },
  labelError: {
    color: colors['Danger/500'],
  },
  required: {
    color: colors['Danger/500'],
  },
  input: {
    borderWidth: 2,
    borderColor: colors['Gray/200'],
    borderRadius: metrics.radius4,
    padding: metrics.spacing3,
    fontSize: 16,
    backgroundColor: colors['white'],
    color: colors['Gray/800'],
    fontWeight: '500',
    minHeight: 30, 
  },
  disabledInput: {
    backgroundColor: colors['Gray/50'],
    color: colors['Gray/500'],
    borderColor: colors['Gray/200'],
  },
  disabledText: {
    color: colors['Gray/500'],
    fontSize: 16,
    fontWeight: '500',
  },
  fieldWithButton: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: metrics.spacing2,
  },
  fieldContainer: {
    flex: 1,
  },
  changeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors['Primary/50'],
    paddingHorizontal: metrics.spacing2,
    paddingVertical: metrics.spacing1,
    borderRadius: metrics.radius2,
    marginTop: 24, // Align with input field
    gap: metrics.spacing1,
  },
  changeButtonText: {
    color: colors['Primary/600'],
    fontSize: 12,
    fontWeight: '500',
  },
  
  
  pickerContainer: {
    borderWidth: 2, 
    borderColor: colors['Gray/200'],
    borderRadius: metrics.radius2, 
    backgroundColor: colors['white'],
    overflow: 'hidden',
    minHeight: 30, 
    justifyContent: 'center', 
    ...Platform.select({
      android: {
        paddingHorizontal: 0,
      },
      ios: {
        paddingHorizontal: metrics.spacing3,
      },
    }),
  },
  pickerContainerError: {
    borderColor: colors['Danger/500'],
  },
  picker: {
    height: 30, 
    width: '100%',
    color: colors['Gray/800'],
    fontSize: 16, 
    fontWeight: '500', 
    ...Platform.select({
      android: {
        marginLeft: metrics.spacing3,
        marginRight: metrics.spacing3,
      },
      ios: {
        marginLeft: 0,
        marginRight: 0,
      },
    }),
  },
  
  formErrorText: {
    color: colors['Danger/600'],
    fontSize: 13,
    marginTop: metrics.spacing1,
    fontWeight: '500',
  },

  // Button
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors['white'],
    padding: metrics.spacing4,
    borderTopWidth: 1,
    borderTopColor: colors['Gray/200'],
    shadowColor: colors['black'],
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  submitButton: {
    backgroundColor: colors['Primary/500'],
    borderRadius: metrics.radius4,
    paddingVertical: metrics.spacing4,
    shadowColor: colors['Primary/500'],
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 8,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '700',
    backgroundColor: colors['Moss/500']
  },

  // Loading
  loadingText: {
    fontSize: 16,
    color: colors['Gray/600'],
    fontWeight: '500',
  },
});

export default ProfilePanel;