import { useEffect, useState, useCallback } from 'react';
import { getKeytoDeleteAccount, deleteAccount } from 'api/users';

export const useDeleteAccount = () => {
  const [loading, setLoading] = useState(false);
  const [deleteKey, setDeleteKey] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [countdown, setCountdown] = useState(30);
  const [canResend, setCanResend] = useState(false);

  const fetchDeleteKey = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('useDeleteAccount - Fetching delete key');
      const response = await getKeytoDeleteAccount();
      console.log('Delete key:', response);
      setDeleteKey(response.deleteKey);
      setCountdown(30); // Reset countdown
      setCanResend(false);
    } catch (error) {
      console.error('useDeleteAccount - Error fetching delete key:', error);
      setError('Không thể lấy mã OTP xóa tài khoản');
      setDeleteKey(null);
    } finally {
      setLoading(false);
    }
  }, []);

  const resendOtp = useCallback(async () => {
    setCountdown(30);
    setCanResend(false);
    await fetchDeleteKey();
  }, [fetchDeleteKey]);

  useEffect(() => {
    fetchDeleteKey();
  }, [fetchDeleteKey]);

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [countdown]);

  return {
    loading,
    deleteKey,
    error,
    countdown,
    canResend,
    deleteAccount,
    resendOtp,
  };
};