import React, { useState, useRef, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Ionicons from 'react-native-vector-icons/Ionicons';
import colors from 'themes/colors';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import { REGEX_INPUT } from 'constants/authentication';
import { sendOtp, updatePhone } from 'api/users';
import Toast from 'react-native-toast-message';

interface ChangePhoneModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: (newPhone: string) => void;
  currentPhone?: string;
}

const phoneSchema = yup.object({
  newPhone: yup
    .string()
    .required('Số điện thoại mới là bắt buộc')
    .trim()
    .matches(REGEX_INPUT.PHONE, 'Định dạng số điện thoại không hợp lệ'),
});

const otpSchema = yup.object({
  otp: yup
    .string()
    .required('Mã OTP là bắt buộc')
    .trim()
    .length(6, 'Mã OTP phải có 6 chữ số'),
});

const ChangePhoneModal: React.FC<ChangePhoneModalProps> = ({
  visible,
  onClose,
  onSuccess,
  currentPhone,
}) => {
  const [step, setStep] = useState<'phone' | 'otp'>('phone');
  const [loading, setLoading] = useState(false);
  const [newPhone, setNewPhone] = useState('');
  const [countdown, setCountdown] = useState(0);
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  const phoneForm = useForm<{ newPhone: string }>({
    resolver: yupResolver(phoneSchema),
    defaultValues: { newPhone: '' },
  });

  const otpForm = useForm<{ otp: string }>({
    resolver: yupResolver(otpSchema),
    defaultValues: { otp: '' },
  });

  useEffect(() => {
    if (visible) {
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }).start();
    } else {
      scaleAnim.setValue(0.9);
      setStep('phone');
      setNewPhone('');
      setCountdown(0);
      phoneForm.reset();
      otpForm.reset();
    }
  }, [visible, scaleAnim, phoneForm, otpForm]);

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [countdown]);

  const handleSendOtp = async (data: { newPhone: string }) => {
    if (data.newPhone === currentPhone) {
      Toast.show({
        type: 'error',
        text1: 'Số điện thoại mới không được trùng với số hiện tại',
      });
      return;
    }

    setLoading(true);
    try {
      await sendOtp({ phoneNumber: data.newPhone });
      setNewPhone(data.newPhone);
      setStep('otp');
      setCountdown(60);
      Toast.show({
        type: 'success',
        text1: 'Mã OTP đã được gửi đến số điện thoại mới',
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: error?.message || 'Không thể gửi mã OTP',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async (data: { otp: string }) => {
    setLoading(true);
    try {
      await updatePhone({ body: { newPhone, otp: data.otp } });
      Toast.show({
        type: 'success',
        text1: 'Cập nhật số điện thoại thành công',
      });
      onSuccess(newPhone);
      onClose();
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: error?.message || 'Mã OTP không đúng',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResendOtp = async () => {
    if (countdown > 0) return;
    
    setLoading(true);
    try {
      await sendOtp({ phoneNumber: newPhone });
      setCountdown(60);
      Toast.show({
        type: 'success',
        text1: 'Mã OTP đã được gửi lại',
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: error?.message || 'Không thể gửi lại mã OTP',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.container,
            { transform: [{ scale: scaleAnim }] },
          ]}
        >
          <View style={styles.header}>
            <Text style={styles.title}>
              {step === 'phone' ? 'Thay đổi số điện thoại' : 'Xác nhận OTP'}
            </Text>
            <TouchableOpacity
              onPress={handleClose}
              style={styles.closeButton}
              disabled={loading}
            >
              <Ionicons name="close" size={24} color={colors['Gray/600']} />
            </TouchableOpacity>
          </View>

          {step === 'phone' ? (
            <View style={styles.content}>
              <Text style={styles.description}>
                {currentPhone ? (
                  <>
                    Số điện thoại hiện tại: <Text style={styles.currentPhone}>{currentPhone}</Text>
                  </>
                ) : (
                  'Bạn chưa có số điện thoại'
                )}
              </Text>
              <Text style={styles.description}>
                Nhập số điện thoại mới để nhận mã OTP:
              </Text>
              
              <Controller
                control={phoneForm.control}
                name="newPhone"
                render={({ field: { onChange, onBlur, value } }) => (
                  <View style={styles.inputContainer}>
                    <TextInput
                      style={styles.input}
                      placeholder="Nhập số điện thoại mới"
                      placeholderTextColor={colors['Gray/400']}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      keyboardType="phone-pad"
                      editable={!loading}
                    />
                  </View>
                )}
              />
              {phoneForm.formState.errors.newPhone && (
                <Text style={styles.errorText}>
                  {phoneForm.formState.errors.newPhone.message}
                </Text>
              )}

              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[styles.button, styles.cancelButton]}
                  onPress={handleClose}
                  disabled={loading}
                >
                  <Text style={styles.cancelButtonText}>Hủy</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.button, styles.primaryButton, loading && styles.disabledButton]}
                  onPress={phoneForm.handleSubmit(handleSendOtp)}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator size="small" color={colors.white} />
                  ) : (
                    <Text style={styles.primaryButtonText}>Gửi OTP</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={styles.content}>
              <Text style={styles.description}>
                Mã OTP đã được gửi đến: <Text style={styles.newPhone}>{newPhone}</Text>
              </Text>
              <Text style={styles.description}>
                Vui lòng nhập mã OTP gồm 6 chữ số:
              </Text>
              
              <Controller
                control={otpForm.control}
                name="otp"
                render={({ field: { onChange, onBlur, value } }) => (
                  <View style={styles.inputContainer}>
                    <TextInput
                      style={styles.input}
                      placeholder="Nhập mã OTP"
                      placeholderTextColor={colors['Gray/400']}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      keyboardType="numeric"
                      maxLength={6}
                      editable={!loading}
                    />
                  </View>
                )}
              />
              {otpForm.formState.errors.otp && (
                <Text style={styles.errorText}>
                  {otpForm.formState.errors.otp.message}
                </Text>
              )}

              <TouchableOpacity
                style={[styles.resendButton, countdown > 0 && styles.disabledButton]}
                onPress={handleResendOtp}
                disabled={countdown > 0 || loading}
              >
                <Text style={[styles.resendButtonText, countdown > 0 && styles.disabledText]}>
                  {countdown > 0 ? `Gửi lại sau ${countdown}s` : 'Gửi lại OTP'}
                </Text>
              </TouchableOpacity>

              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[styles.button, styles.cancelButton]}
                  onPress={() => setStep('phone')}
                  disabled={loading}
                >
                  <Text style={styles.cancelButtonText}>Quay lại</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.button, styles.primaryButton, loading && styles.disabledButton]}
                  onPress={otpForm.handleSubmit(handleVerifyOtp)}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator size="small" color={colors.white} />
                  ) : (
                    <Text style={styles.primaryButtonText}>Xác nhận</Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          )}
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing4,
  },
  container: {
    backgroundColor: colors.white,
    borderRadius: metrics.radius4,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: metrics.spacing4,
    borderBottomWidth: 1,
    borderBottomColor: colors['Gray/200'],
  },
  title: {
    ...fonts.style.h4,
    fontWeight: '600',
    color: colors['Gray/800'],
  },
  closeButton: {
    padding: metrics.spacing1,
  },
  content: {
    padding: metrics.spacing4,
  },
  description: {
    ...fonts.style.normal,
    color: colors['Gray/600'],
    marginBottom: metrics.spacing3,
    lineHeight: 20,
  },
  currentPhone: {
    fontWeight: '600',
    color: colors['Primary/600'],
  },
  newPhone: {
    fontWeight: '600',
    color: colors['Success/600'],
  },
  inputContainer: {
    marginBottom: metrics.spacing2,
  },
  input: {
    borderWidth: 1,
    borderColor: colors['Gray/300'],
    borderRadius: metrics.radius3,
    padding: metrics.spacing3,
    ...fonts.style.normal,
    color: colors['Gray/800'],
    backgroundColor: colors.white,
  },
  errorText: {
    ...fonts.style.small,
    color: colors['Danger/600'],
    marginBottom: metrics.spacing2,
  },
  resendButton: {
    alignSelf: 'flex-end',
    marginBottom: metrics.spacing3,
  },
  resendButtonText: {
    ...fonts.style.small,
    color: colors['Primary/600'],
    fontWeight: '500',
  },
  disabledText: {
    color: colors['Gray/400'],
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: metrics.spacing2,
  },
  button: {
    flex: 1,
    paddingVertical: metrics.spacing3,
    borderRadius: metrics.radius3,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: colors['Gray/100'],
  },
  cancelButtonText: {
    ...fonts.style.normal,
    color: colors['Gray/700'],
    fontWeight: '500',
  },
  primaryButton: {
    backgroundColor: colors['Primary/500'],
  },
  primaryButtonText: {
    ...fonts.style.normal,
    color: colors.white,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default ChangePhoneModal;
